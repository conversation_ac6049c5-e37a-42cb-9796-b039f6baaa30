'use client';

import { Spinner } from '@telegram-apps/telegram-ui';
import { Loader2 } from 'lucide-react';
import { forwardRef } from 'react';

import { ItemCacheProvider } from '@/components/ui/virtualized-grid';
import type { CollectionEntity, OrderEntity } from '@/core.constants';

import { VirtualizedCard } from '@/components/ui/virtualized-card';

interface MarketplaceOrderListProps {
  orders: OrderEntity[];
  collections: CollectionEntity[];
  loading: boolean;
  loadingMore: boolean;
  emptyMessage: string;
  onOrderClick: (order: OrderEntity) => void;
}

export const MarketplaceOrderList = forwardRef<
  HTMLDivElement,
  MarketplaceOrderListProps
>(
  (
    { orders, collections, loading, loadingMore, emptyMessage, onOrderClick },
    ref,
  ) => {
    if (loading) {
      return (
        <div className="text-center py-8">
          <Spinner className="flex justify-center" size="l" />
          <p className="text-[#708499] mt-2">Loading orders...</p>
        </div>
      );
    }

    if (orders.length === 0) {
      return (
        <div className="text-center py-8">
          <p className="text-[#708499]">{emptyMessage}</p>
        </div>
      );
    }

    return (
      <>
        <ItemCacheProvider>
          <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
            {orders.map((order, index) => (
              <VirtualizedCard
                variant="order"
                animated
                key={`${order.id}-${index}`}
                order={order}
                collection={collections.find(
                  (c) => c.id === order.collectionId,
                )}
                onClick={() => onOrderClick(order)}
                index={index}
                initialRenderedCount={8}
              />
            ))}
          </div>
        </ItemCacheProvider>

        <div
          ref={ref}
          className="flex justify-center py-4 w-full"
          style={{
            height: '60px',
            minHeight: '60px',
            backgroundColor: 'transparent',
          }}
        >
          {loadingMore && (
            <div className="flex items-center gap-2 text-gray-400">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Loading more orders...</span>
            </div>
          )}
        </div>
      </>
    );
  },
);
