'use client';

import { getAnalytics, isSupported } from 'firebase/analytics';
import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getFunctions } from 'firebase/functions';
import { getStorage } from 'firebase/storage';
import { useRouter } from 'next/navigation';
import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState } from 'react';

import { getAppConfig } from '@/api/app-config-api';
import { getUserById, getUserRole } from '@/api/auth-api';
import { getAllCollections } from '@/api/collection-api';

import { AppCacheProvider } from './contexts/AppCacheContext';
import type {
  AppConfigEntity,
  CollectionEntity,
  UserEntity,
} from './core.constants';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

const app = initializeApp(firebaseConfig);
export const firestore = getFirestore(app);
export const firebaseStorage = getStorage(app);
export const firebaseAuth = getAuth();
export const firebaseFunctions = getFunctions(app);
export const analytics = isSupported().then((yes) =>
  yes ? getAnalytics(app) : null,
);

interface RootContextType {
  resetUser: () => void;
  currentUser: UserEntity | null;
  role: string;
  collections: CollectionEntity[];
  appConfig: AppConfigEntity | null;
  setCurrentUser: (user: UserEntity | null) => void;
  setRole: (role: string) => void;
  setCollections: (collections: CollectionEntity[]) => void;
  setAppConfig: (config: AppConfigEntity | null) => void;
  refreshCollections: () => Promise<void>;
  refreshAppConfig: () => Promise<void>;
  refetchUser: () => Promise<void>;
}

const RootContext = createContext<RootContextType | undefined>(undefined);

export const useRootContext = () => {
  const context = useContext(RootContext);
  if (context === undefined) {
    throw new Error('useRootContext must be used within a RootProvider');
  }
  return context;
};

export const RootProvider = ({ children }: { children: ReactNode }) => {
  const [role, setRole] = useState('');
  const [currentUser, setCurrentUser] = useState<UserEntity | null>(null);
  const [collections, setCollections] = useState<CollectionEntity[]>([]);
  const [appConfig, setAppConfig] = useState<AppConfigEntity | null>(null);
  const router = useRouter();

  // Refresh functions
  const refreshCollections = async () => {
    try {
      const collections = await getAllCollections();
      setCollections(collections);
    } catch (error) {
      console.error('Error loading collections:', error);
    }
  };

  const refreshAppConfig = async () => {
    try {
      const config = await getAppConfig();
      setAppConfig(config);
    } catch (error) {
      console.error('Error loading app config:', error);
    }
  };

  const refetchUser = async () => {
    try {
      const currentAuthUser = firebaseAuth.currentUser;
      if (currentAuthUser) {
        const user = await getUserById(currentAuthUser.uid);
        setCurrentUserWithValidation(user);
      } else {
        console.warn('No authenticated user found to refetch');
      }
    } catch (error) {
      console.error('Error refetching user:', error);
    }
  };

  useEffect(() => {
    const unsubscribe = firebaseAuth.onAuthStateChanged((user) => {
      if (user) {
        getUserById(user.uid).then((user) => {
          setCurrentUserWithValidation(user);
        });

        getUserRole().then((role) => {
          if (!role) {
            console.warn('No role found for user');
            return;
          }

          setRole(role);
          if (
            role !== 'admin' &&
            typeof window !== 'undefined' &&
            window.location.pathname.startsWith('/admin')
          ) {
            router.push('/');
          }
        });
      }
    });

    return () => unsubscribe();
  }, [router]);

  useEffect(() => {
    refreshCollections();
    refreshAppConfig();
  }, []);

  // Enhanced setCurrentUser with balance validation
  const setCurrentUserWithValidation = (user: UserEntity | null) => {
    if (user) {
      // Check if balance field exists
      if (!user.balance) {
        // Create balance field with default values
        user.balance = {
          sum: 0,
          locked: 0,
        };
      } else {
        // Balance exists, check for missing fields
        if (typeof user.balance.sum !== 'number') {
          user.balance.sum = 0;
        }
        if (typeof user.balance.locked !== 'number') {
          user.balance.locked = 0;
        }
      }
    }
    setCurrentUser(user);
  };

  const resetUser = () => {
    setRole('');
    setCurrentUser(null);
    setCollections([]);
    setAppConfig(null);
  };

  return (
    <AppCacheProvider>
      <RootContext.Provider
        value={{
          resetUser,
          currentUser,
          role,
          collections,
          appConfig,
          setCurrentUser: setCurrentUserWithValidation,
          setRole,
          setCollections,
          setAppConfig,
          refreshCollections,
          refreshAppConfig,
          refetchUser,
        }}
      >
        {children}
      </RootContext.Provider>
    </AppCacheProvider>
  );
};
